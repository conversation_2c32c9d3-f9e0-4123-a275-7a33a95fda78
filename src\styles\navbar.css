/* =============================================
   🏛️ BAKASANA NAVBAR STYLES - CLEAN & OPTIMIZED
   ============================================= */

/* ===== NAVBAR ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulseGentle {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* ===== NAVBAR UTILITY CLASSES ===== */
.animate-pulse-gentle {
  animation: pulseGentle 2s ease-in-out infinite;
}

/* ===== DROPDOWN SCROLLBAR ===== */
.dropdown-content::-webkit-scrollbar {
  width: 4px;
}

.dropdown-content::-webkit-scrollbar-track {
  background: rgba(139, 115, 85, 0.1);
  border-radius: 2px;
}

.dropdown-content::-webkit-scrollbar-thumb {
  background: rgba(139, 115, 85, 0.3);
  border-radius: 2px;
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 115, 85, 0.5);
}

/* ===== ENHANCED HOVER EFFECTS ===== */
.nav-item-hover {
  position: relative;
  overflow: hidden;
}

.nav-item-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(139, 115, 85, 0.1), transparent);
  transition: left 0.5s;
}

.nav-item-hover:hover::before {
  left: 100%;
}

/* ===== SHIMMER EFFECT ===== */
.shimmer-effect {
  animation: shimmer 2s infinite;
}

/* ===== RESPONSIVE OPTIMIZATIONS ===== */
@media (max-width: 1023px) {
  .mobile-menu-item {
    animation: fadeInUp 0.6s ease-out both;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  .animate-pulse-gentle,
  .shimmer-effect,
  .nav-item-hover::before {
    animation: none !important;
  }
  
  .nav-item-hover:hover::before {
    left: 0;
    opacity: 0.1;
  }
}

/* ===== FOCUS STATES ===== */
.navbar-focus:focus {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* ===== DROPDOWN ENHANCEMENTS ===== */
.dropdown-arrow {
  transition: transform 0.3s ease;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

.dropdown-menu {
  transform-origin: top center;
}

.dropdown-menu.open {
  animation: fadeInUp 0.3s ease-out;
}

.dropdown-menu.closed {
  animation: fadeInUp 0.3s ease-out reverse;
}
